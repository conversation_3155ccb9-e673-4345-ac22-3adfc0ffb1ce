# WebFlowMaster Environment Configuration
# Copy this file to .env and update the values

# Application Environment
NODE_ENV=development
PORT=5000

# Database Configuration
# Replace with your actual PostgreSQL connection string
DATABASE_URL=postgresql://username:password@localhost:5432/webflowmaster

# Security Configuration
# Generate strong secrets using: openssl rand -base64 32
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
SESSION_SECRET=your-super-secret-session-key-at-least-32-characters-long

# CORS Configuration
# Comma-separated list of allowed origins
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=csv,xlsx,xls,json,xml,md

# Redis Configuration (Optional - for caching)
# REDIS_URL=redis://localhost:6379

# Logging Configuration
LOG_LEVEL=info

# Monitoring
ENABLE_METRICS=true

# Production Security Settings (uncomment for production)
# FORCE_HTTPS=true
# SECURE_COOKIES=true
# TRUST_PROXY=true
