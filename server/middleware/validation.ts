import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema, ZodError } from 'zod';
import { errorLogger } from '../utils/logger.js';

/**
 * Request validation middleware using Zod schemas
 * Provides type-safe validation with detailed error messages
 */

/**
 * Validation target types
 */
type ValidationTarget = 'body' | 'query' | 'params';

/**
 * Validation options
 */
interface ValidationOptions {
  target?: ValidationTarget;
  stripUnknown?: boolean;
  abortEarly?: boolean;
}

/**
 * Generic validation middleware factory
 * Creates middleware that validates request data against Zod schema
 */
export function validateRequest<T>(
  schema: ZodSchema<T>,
  options: ValidationOptions = {}
) {
  const {
    target = 'body',
    stripUnknown = true,
    abortEarly = true,
  } = options;

  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Get data to validate based on target
      const dataToValidate = req[target];

      // Parse and validate data
      const validatedData = schema.parse(dataToValidate);

      // Replace original data with validated data
      if (stripUnknown) {
        req[target] = validatedData;
      }

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        // Format validation errors
        const formattedErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
          received: err.received,
        }));

        // Log validation error
        errorLogger.logValidationError(formattedErrors, {
          endpoint: req.path,
          method: req.method,
          target,
          ip: req.ip,
        });

        res.status(400).json({
          error: 'Validation failed',
          message: 'Request data is invalid',
          details: formattedErrors,
        });
        return;
      }

      // Handle unexpected errors
      errorLogger.logError(error as Error, {
        endpoint: req.path,
        method: req.method,
        target,
        ip: req.ip,
      });

      res.status(500).json({
        error: 'Validation error',
        message: 'An unexpected error occurred during validation',
      });
    }
  };
}

/**
 * Validate request body
 */
export function validateBody<T>(schema: ZodSchema<T>) {
  return validateRequest(schema, { target: 'body' });
}

/**
 * Validate query parameters
 */
export function validateQuery<T>(schema: ZodSchema<T>) {
  return validateRequest(schema, { target: 'query' });
}

/**
 * Validate route parameters
 */
export function validateParams<T>(schema: ZodSchema<T>) {
  return validateRequest(schema, { target: 'params' });
}

/**
 * Common validation schemas
 */
export const commonSchemas = {
  // ID parameter validation
  id: z.object({
    id: z.string().regex(/^\d+$/, 'ID must be a positive integer').transform(Number),
  }),

  // Pagination query validation
  pagination: z.object({
    page: z.string().optional().default('1').transform(Number),
    limit: z.string().optional().default('10').transform(Number),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
  }).refine(data => data.page > 0, {
    message: 'Page must be greater than 0',
    path: ['page'],
  }).refine(data => data.limit > 0 && data.limit <= 100, {
    message: 'Limit must be between 1 and 100',
    path: ['limit'],
  }),

  // Date range validation
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  }).refine(data => {
    if (data.startDate && data.endDate) {
      return new Date(data.startDate) <= new Date(data.endDate);
    }
    return true;
  }, {
    message: 'Start date must be before or equal to end date',
    path: ['dateRange'],
  }),

  // File upload validation
  fileUpload: z.object({
    filename: z.string().min(1, 'Filename is required'),
    mimetype: z.string().min(1, 'MIME type is required'),
    size: z.number().positive('File size must be positive'),
  }),
};

/**
 * Business-specific validation schemas
 */
export const businessSchemas = {
  // Mission validation
  mission: z.object({
    name: z.string().min(1, 'Mission name is required').max(255, 'Mission name too long'),
    courseNumber: z.number().int().positive('Course number must be a positive integer'),
  }),

  // Transaction validation
  transaction: z.object({
    date: z.string().regex(/^\d{4}-\d{2}$/, 'Date must be in YYYY-MM format'),
    type: z.enum(['savings', 'discount'], {
      errorMap: () => ({ message: 'Type must be either "savings" or "discount"' }),
    }),
    amount: z.number().positive('Amount must be positive'),
    missionId: z.number().int().positive().optional(),
  }),

  // Expense validation
  expense: z.object({
    description: z.string().min(1, 'Description is required').max(500, 'Description too long'),
    amount: z.number().positive('Amount must be positive'),
    categoryId: z.number().int().positive('Category ID is required'),
  }),

  // Employee validation
  employee: z.object({
    brigade: z.string().min(1, 'Brigade is required').max(100, 'Brigade too long'),
    fileNumber: z.string().min(1, 'File number is required').max(50, 'File number too long'),
    name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
    ci: z.string().min(1, 'CI is required').max(20, 'CI too long'),
    signature: z.string().max(255, 'Signature too long').optional(),
    costCenter: z.string().min(1, 'Cost center is required').max(100, 'Cost center too long'),
    organizationalUnit: z.string().min(1, 'Organizational unit is required').max(100, 'Organizational unit too long'),
  }),

  // Payroll report validation
  payrollReport: z.object({
    year: z.number().int().min(2000, 'Year must be 2000 or later').max(2100, 'Year must be 2100 or earlier'),
    month: z.number().int().min(1, 'Month must be between 1 and 12').max(12, 'Month must be between 1 and 12'),
    exchangeRate: z.number().positive('Exchange rate must be positive'),
  }),

  // Payroll concept validation
  payrollConcept: z.object({
    name: z.string().min(1, 'Concept name is required').max(255, 'Concept name too long'),
    section: z.enum(['ABONOS', 'DESCUENTOS'], {
      errorMap: () => ({ message: 'Section must be either "ABONOS" or "DESCUENTOS"' }),
    }),
    isActive: z.number().int().min(0).max(1).default(1),
    displayOrder: z.number().int().min(0).default(0),
  }),
};

/**
 * Validation error formatter
 * Formats Zod errors into user-friendly messages
 */
export function formatValidationError(error: ZodError): any {
  return {
    error: 'Validation failed',
    message: 'The provided data is invalid',
    details: error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
      received: err.received,
    })),
  };
}

/**
 * Async validation middleware
 * For validations that require database checks
 */
export function validateAsync<T>(
  asyncValidator: (data: any) => Promise<T>
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const validatedData = await asyncValidator(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof Error) {
        res.status(400).json({
          error: 'Validation failed',
          message: error.message,
        });
        return;
      }

      res.status(500).json({
        error: 'Validation error',
        message: 'An unexpected error occurred during validation',
      });
    }
  };
}

/**
 * Conditional validation middleware
 * Applies validation only when certain conditions are met
 */
export function validateConditional<T>(
  schema: ZodSchema<T>,
  condition: (req: Request) => boolean,
  options: ValidationOptions = {}
) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (condition(req)) {
      return validateRequest(schema, options)(req, res, next);
    }
    next();
  };
}
