import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { logger, errorLogger } from '../utils/logger.js';
import { config } from '../config/index.js';

/**
 * Custom error classes for different types of application errors
 * Follows Single Responsibility Principle
 */

/**
 * Base application error class
 */
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly errorCode?: string;
  public readonly context?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    errorCode?: string,
    context?: any
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.errorCode = errorCode;
    this.context = context;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error class
 */
export class ValidationError extends AppError {
  public readonly details: any[];

  constructor(message: string, details: any[] = [], context?: any) {
    super(message, 400, true, 'VALIDATION_ERROR', context);
    this.details = details;
  }
}

/**
 * Business logic error class
 */
export class BusinessError extends AppError {
  constructor(message: string, context?: any) {
    super(message, 422, true, 'BUSINESS_ERROR', context);
  }
}

/**
 * Not found error class
 */
export class NotFoundError extends AppError {
  constructor(resource: string, identifier?: string | number) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    super(message, 404, true, 'NOT_FOUND', { resource, identifier });
  }
}

/**
 * Unauthorized error class
 */
export class UnauthorizedError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, true, 'UNAUTHORIZED');
  }
}

/**
 * Forbidden error class
 */
export class ForbiddenError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, true, 'FORBIDDEN');
  }
}

/**
 * Conflict error class
 */
export class ConflictError extends AppError {
  constructor(message: string, context?: any) {
    super(message, 409, true, 'CONFLICT', context);
  }
}

/**
 * Rate limit error class
 */
export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT');
  }
}

/**
 * Database error class
 */
export class DatabaseError extends AppError {
  constructor(message: string, originalError?: Error) {
    super(message, 500, true, 'DATABASE_ERROR', { originalError: originalError?.message });
  }
}

/**
 * External service error class
 */
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, statusCode: number = 502) {
    super(`External service error (${service}): ${message}`, statusCode, true, 'EXTERNAL_SERVICE_ERROR', { service });
  }
}

/**
 * Error response interface
 */
interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
  path: string;
  method: string;
  errorCode?: string;
  details?: any;
  stack?: string;
  requestId?: string;
}

/**
 * Global error handling middleware
 * Handles all types of errors in a consistent manner
 */
export const globalErrorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Skip if response already sent
  if (res.headersSent) {
    return next(error);
  }

  // Generate request ID for tracking
  const requestId = req.headers['x-request-id'] as string || generateRequestId();

  // Default error values
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errorCode: string | undefined;
  let details: any;

  // Handle different error types
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    errorCode = error.errorCode;
    
    if (error instanceof ValidationError) {
      details = error.details;
    }
  } else if (error instanceof ZodError) {
    statusCode = 400;
    message = 'Validation failed';
    errorCode = 'VALIDATION_ERROR';
    details = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
      received: err.received,
    }));
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid data format';
    errorCode = 'CAST_ERROR';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    errorCode = 'INVALID_TOKEN';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    errorCode = 'TOKEN_EXPIRED';
  } else if (error.message.includes('duplicate key')) {
    statusCode = 409;
    message = 'Resource already exists';
    errorCode = 'DUPLICATE_RESOURCE';
  }

  // Log error based on severity
  const logContext = {
    error: error.message,
    stack: error.stack,
    statusCode,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId,
    userId: (req as any).user?.id,
  };

  if (statusCode >= 500) {
    errorLogger.logError(error, logContext);
  } else if (statusCode >= 400) {
    logger.warn('Client error', logContext);
  }

  // Prepare error response
  const errorResponse: ErrorResponse = {
    error: getErrorName(statusCode),
    message: config.app.isProduction && statusCode >= 500 ? 'Internal Server Error' : message,
    statusCode,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
    requestId,
  };

  // Add optional fields
  if (errorCode) {
    errorResponse.errorCode = errorCode;
  }

  if (details) {
    errorResponse.details = details;
  }

  // Include stack trace in development
  if (config.app.isDevelopment && error.stack) {
    errorResponse.stack = error.stack;
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
};

/**
 * Async error wrapper
 * Catches async errors and passes them to error handler
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found handler
 * Handles requests to non-existent routes
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError('Route', req.path);
  next(error);
};

/**
 * Unhandled rejection handler
 * Handles unhandled promise rejections
 */
export const unhandledRejectionHandler = (reason: any, promise: Promise<any>): void => {
  logger.error('Unhandled Promise Rejection', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString(),
  });

  // In production, you might want to gracefully shutdown
  if (config.app.isProduction) {
    process.exit(1);
  }
};

/**
 * Uncaught exception handler
 * Handles uncaught exceptions
 */
export const uncaughtExceptionHandler = (error: Error): void => {
  logger.error('Uncaught Exception', {
    error: error.message,
    stack: error.stack,
  });

  // Gracefully shutdown
  process.exit(1);
};

/**
 * Helper functions
 */

function getErrorName(statusCode: number): string {
  const errorNames: Record<number, string> = {
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    409: 'Conflict',
    422: 'Unprocessable Entity',
    429: 'Too Many Requests',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
  };

  return errorNames[statusCode] || 'Unknown Error';
}

function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Error factory functions for common errors
 */
export const createError = {
  validation: (message: string, details?: any[], context?: any) => 
    new ValidationError(message, details, context),
  
  business: (message: string, context?: any) => 
    new BusinessError(message, context),
  
  notFound: (resource: string, identifier?: string | number) => 
    new NotFoundError(resource, identifier),
  
  unauthorized: (message?: string) => 
    new UnauthorizedError(message),
  
  forbidden: (message?: string) => 
    new ForbiddenError(message),
  
  conflict: (message: string, context?: any) => 
    new ConflictError(message, context),
  
  rateLimit: (message?: string) => 
    new RateLimitError(message),
  
  database: (message: string, originalError?: Error) => 
    new DatabaseError(message, originalError),
  
  externalService: (service: string, message: string, statusCode?: number) => 
    new ExternalServiceError(service, message, statusCode),
};

/**
 * Setup error handlers
 */
export function setupErrorHandlers(): void {
  // Handle unhandled promise rejections
  process.on('unhandledRejection', unhandledRejectionHandler);
  
  // Handle uncaught exceptions
  process.on('uncaughtException', uncaughtExceptionHandler);
  
  logger.info('Error handlers configured');
}
