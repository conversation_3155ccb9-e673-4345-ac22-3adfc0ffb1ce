import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { 
  PasswordService, 
  TokenService, 
  authSchemas, 
  UserRole,
  authSecurityStack 
} from '../middleware/auth.js';
import { securityLogger, errorLogger } from '../utils/logger.js';
import { validateRequest } from '../middleware/validation.js';

/**
 * Authentication routes with comprehensive security
 * Implements secure authentication following OWASP guidelines
 */

const router = Router();

// Apply security middleware to all auth routes
router.use(authSecurityStack);

/**
 * User registration endpoint
 * Creates new user account with role-based access
 */
router.post('/register', validateRequest(authSchemas.register), async (req: Request, res: Response) => {
  try {
    const { email, password, name, role } = req.body;

    // Check if user already exists (in real app, check database)
    // For demo purposes, we'll simulate this check
    const existingUser = false; // await userService.findByEmail(email);
    
    if (existingUser) {
      securityLogger.loginFailure(email, req.ip, 'User already exists');
      return res.status(409).json({
        error: 'Registration failed',
        message: 'User with this email already exists',
      });
    }

    // Hash password
    const hashedPassword = await PasswordService.hash(password);

    // Create user (in real app, save to database)
    const newUser = {
      id: generateUserId(),
      email,
      name,
      role: role || UserRole.READONLY,
      isActive: true,
      createdAt: new Date(),
      passwordHash: hashedPassword,
    };

    // Generate JWT token
    const token = TokenService.generateToken(newUser);

    // Log successful registration
    securityLogger.loginSuccess(newUser.id, email, req.ip);

    res.status(201).json({
      message: 'Registration successful',
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
      },
      token,
    });
  } catch (error) {
    errorLogger.logError(error as Error, { 
      endpoint: '/auth/register',
      ip: req.ip,
      email: req.body?.email 
    });

    res.status(500).json({
      error: 'Registration failed',
      message: 'Internal server error',
    });
  }
});

/**
 * User login endpoint
 * Authenticates user and returns JWT token
 */
router.post('/login', validateRequest(authSchemas.login), async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Find user by email (in real app, query database)
    // For demo purposes, we'll simulate a user
    const user = await findUserByEmail(email);
    
    if (!user) {
      securityLogger.loginFailure(email, req.ip, 'User not found');
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid email or password',
      });
    }

    // Check if user is active
    if (!user.isActive) {
      securityLogger.loginFailure(email, req.ip, 'Account disabled');
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Account has been disabled',
      });
    }

    // Verify password
    const isValidPassword = await PasswordService.verify(password, user.passwordHash);
    
    if (!isValidPassword) {
      securityLogger.loginFailure(email, req.ip, 'Invalid password');
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid email or password',
      });
    }

    // Generate JWT token
    const token = TokenService.generateToken(user);

    // Update last login (in real app, update database)
    user.lastLogin = new Date();

    // Log successful login
    securityLogger.loginSuccess(user.id, email, req.ip);

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        lastLogin: user.lastLogin,
      },
      token,
    });
  } catch (error) {
    errorLogger.logError(error as Error, { 
      endpoint: '/auth/login',
      ip: req.ip,
      email: req.body?.email 
    });

    res.status(500).json({
      error: 'Authentication failed',
      message: 'Internal server error',
    });
  }
});

/**
 * Token refresh endpoint
 * Refreshes JWT token for authenticated users
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      return res.status(401).json({
        error: 'Token refresh failed',
        message: 'No token provided',
      });
    }

    // Verify current token
    const payload = TokenService.verifyToken(token);
    
    // Find user (in real app, query database)
    const user = await findUserById(payload.userId);
    
    if (!user || !user.isActive) {
      return res.status(401).json({
        error: 'Token refresh failed',
        message: 'User not found or inactive',
      });
    }

    // Generate new token
    const newToken = TokenService.generateToken(user);

    res.json({
      message: 'Token refreshed successfully',
      token: newToken,
    });
  } catch (error) {
    errorLogger.logError(error as Error, { 
      endpoint: '/auth/refresh',
      ip: req.ip 
    });

    res.status(401).json({
      error: 'Token refresh failed',
      message: 'Invalid or expired token',
    });
  }
});

/**
 * Logout endpoint
 * Logs user logout event
 */
router.post('/logout', async (req: Request, res: Response) => {
  try {
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      const payload = TokenService.verifyToken(token);
      securityLogger.logout(payload.userId, req.ip);
    }

    res.json({
      message: 'Logout successful',
    });
  } catch (error) {
    // Even if token verification fails, we still log out successfully
    res.json({
      message: 'Logout successful',
    });
  }
});

/**
 * Password change endpoint
 * Allows authenticated users to change their password
 */
router.post('/change-password', async (req: Request, res: Response) => {
  try {
    const changePasswordSchema = z.object({
      currentPassword: z.string().min(1, 'Current password is required'),
      newPassword: z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
               'Password must contain uppercase, lowercase, number and special character'),
    });

    const { currentPassword, newPassword } = changePasswordSchema.parse(req.body);
    
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    if (!token) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'No token provided',
      });
    }

    const payload = TokenService.verifyToken(token);
    const user = await findUserById(payload.userId);
    
    if (!user) {
      return res.status(401).json({
        error: 'User not found',
        message: 'Invalid user',
      });
    }

    // Verify current password
    const isValidPassword = await PasswordService.verify(currentPassword, user.passwordHash);
    if (!isValidPassword) {
      securityLogger.loginFailure(user.email, req.ip, 'Invalid current password');
      return res.status(401).json({
        error: 'Password change failed',
        message: 'Current password is incorrect',
      });
    }

    // Hash new password
    const newPasswordHash = await PasswordService.hash(newPassword);
    
    // Update password (in real app, update database)
    user.passwordHash = newPasswordHash;

    res.json({
      message: 'Password changed successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.errors,
      });
    }

    errorLogger.logError(error as Error, { 
      endpoint: '/auth/change-password',
      ip: req.ip 
    });

    res.status(500).json({
      error: 'Password change failed',
      message: 'Internal server error',
    });
  }
});

/**
 * Helper functions (in real app, these would be in a user service)
 */

function generateUserId(): string {
  return 'user_' + Math.random().toString(36).substring(2, 15);
}

async function findUserByEmail(email: string): Promise<any | null> {
  // Simulate database lookup
  // In real app: return await userRepository.findByEmail(email);
  
  if (email === '<EMAIL>') {
    return {
      id: 'user_admin',
      email: '<EMAIL>',
      name: 'Administrator',
      role: UserRole.ADMIN,
      isActive: true,
      passwordHash: await PasswordService.hash('Admin123!'),
      createdAt: new Date(),
    };
  }
  
  return null;
}

async function findUserById(id: string): Promise<any | null> {
  // Simulate database lookup
  // In real app: return await userRepository.findById(id);
  
  if (id === 'user_admin') {
    return {
      id: 'user_admin',
      email: '<EMAIL>',
      name: 'Administrator',
      role: UserRole.ADMIN,
      isActive: true,
      passwordHash: await PasswordService.hash('Admin123!'),
      createdAt: new Date(),
    };
  }
  
  return null;
}

export default router;
